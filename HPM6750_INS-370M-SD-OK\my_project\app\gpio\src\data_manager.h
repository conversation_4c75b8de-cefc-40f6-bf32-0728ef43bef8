/*
 * 数据管理模块头文件
 * 负责5组UART数据的缓存、同步和CAN数据包格式化
 */

#ifndef _DATA_MANAGER_H_
#define _DATA_MANAGER_H_

#include "multi_uart_can_config.h"
#include <stdint.h>
#include <stdbool.h>

/* 数据包头部定义 */
#define DATA_PACKET_HEADER_SIZE    8
#define DATA_PACKET_SYNC_WORD      0xAA55AA55
#define DATA_PACKET_VERSION        0x01

/* CAN数据包类型 */
typedef enum {
    CAN_PACKET_TYPE_DATA = 0x01,      // 数据包
    CAN_PACKET_TYPE_STATUS = 0x02,    // 状态包
    CAN_PACKET_TYPE_ERROR = 0x03      // 错误包
} can_packet_type_t;

/* 数据包头部结构 */
typedef struct __attribute__((packed)) {
    uint32_t sync_word;               // 同步字 0xAA55AA55
    uint8_t version;                  // 版本号
    uint8_t packet_type;              // 包类型
    uint8_t sequence;                 // 序列号
    uint8_t module_mask;              // 模块掩码 (bit0-4对应模块1-5)
    uint32_t timestamp;               // 时间戳
    uint16_t data_length;             // 数据长度
    uint16_t checksum;                // 校验和
} data_packet_header_t;

/* 完整数据包结构 */
typedef struct __attribute__((packed)) {
    data_packet_header_t header;      // 包头
    uint8_t payload[MODULE_COUNT * MODULE_DATA_SIZE]; // 载荷数据
} data_packet_t;

/* CAN帧结构 */
typedef struct {
    uint32_t id;                      // CAN ID
    uint8_t dlc;                      // 数据长度
    uint8_t data[8];                  // 数据
} can_frame_t;

/* 数据同步状态 */
typedef enum {
    SYNC_STATE_IDLE = 0,              // 空闲状态
    SYNC_STATE_COLLECTING,            // 数据收集中
    SYNC_STATE_READY,                 // 数据就绪
    SYNC_STATE_SENDING,               // 发送中
    SYNC_STATE_ERROR                  // 错误状态
} sync_state_t;

/* 模块状态 */
typedef struct {
    uint32_t rx_count;                // 接收计数
    uint32_t error_count;             // 错误计数
    uint32_t last_rx_time;            // 最后接收时间
    bool online;                      // 在线状态
} module_status_t;

/* 系统统计信息 */
typedef struct {
    uint32_t total_packets;           // 总包数
    uint32_t success_packets;         // 成功包数
    uint32_t error_packets;           // 错误包数
    uint32_t timeout_count;           // 超时次数
    uint32_t sync_failures;           // 同步失败次数
    module_status_t module_stats[MODULE_COUNT]; // 模块统计
} system_stats_t;

/* 数据管理器上下文 */
typedef struct {
    data_packet_t current_packet;     // 当前数据包
    sync_state_t sync_state;          // 同步状态
    uint8_t sequence_number;          // 序列号
    uint32_t sync_start_time;         // 同步开始时间
    uint8_t ready_modules;            // 就绪模块掩码
    system_stats_t stats;             // 统计信息
    
    // 环形缓冲区
    uint8_t *ring_buffer[MODULE_COUNT];
    uint32_t ring_head[MODULE_COUNT];
    uint32_t ring_tail[MODULE_COUNT];
    uint32_t ring_size[MODULE_COUNT];
} data_manager_t;

/* 全局数据管理器实例 */
extern data_manager_t g_data_manager;

/* 函数声明 */

/**
 * @brief 初始化数据管理器
 * @return 状态码
 */
hpm_stat_t data_manager_init(void);

/**
 * @brief 添加模块数据到缓冲区
 * @param module_id 模块ID (0-4)
 * @param data 数据指针
 * @param length 数据长度
 * @return 状态码
 */
hpm_stat_t data_manager_add_module_data(uint8_t module_id, const uint8_t *data, uint32_t length);

/**
 * @brief 检查所有模块数据是否就绪
 * @return true: 就绪, false: 未就绪
 */
bool data_manager_is_all_ready(void);

/**
 * @brief 同步并打包所有模块数据
 * @return 状态码
 */
hpm_stat_t data_manager_sync_and_pack(void);

/**
 * @brief 获取打包后的数据包
 * @param packet 输出数据包指针
 * @return 状态码
 */
hpm_stat_t data_manager_get_packet(data_packet_t **packet);

/**
 * @brief 将数据包分割为CAN帧
 * @param packet 输入数据包
 * @param frames 输出CAN帧数组
 * @param frame_count 输出帧数量
 * @return 状态码
 */
hpm_stat_t data_manager_split_to_can_frames(const data_packet_t *packet, 
                                           can_frame_t *frames, 
                                           uint8_t *frame_count);

/**
 * @brief 重置数据管理器状态
 */
void data_manager_reset(void);

/**
 * @brief 处理超时情况
 */
void data_manager_handle_timeout(void);

/**
 * @brief 获取系统统计信息
 * @return 统计信息指针
 */
const system_stats_t* data_manager_get_stats(void);

/**
 * @brief 更新模块状态
 * @param module_id 模块ID
 * @param online 在线状态
 */
void data_manager_update_module_status(uint8_t module_id, bool online);

/**
 * @brief 计算校验和
 * @param data 数据指针
 * @param length 数据长度
 * @return 校验和
 */
uint16_t calculate_checksum(const uint8_t *data, uint32_t length);

/**
 * @brief 验证数据包完整性
 * @param packet 数据包指针
 * @return true: 有效, false: 无效
 */
bool validate_data_packet(const data_packet_t *packet);

/**
 * @brief 获取当前时间戳
 * @return 时间戳 (毫秒)
 */
uint32_t get_timestamp_ms(void);

/* 调试和监控函数 */
#ifdef DEBUG_DATA_MANAGER
void data_manager_print_stats(void);
void data_manager_dump_packet(const data_packet_t *packet);
#endif

#endif /* _DATA_MANAGER_H_ */
