# CMakeLists.txt for Multi-UART+CAN Communication System
# Based on HPM6750_INS-370M-SD-OK project

cmake_minimum_required(VERSION 3.13)

# Project configuration
set(CONFIG_HPM6750 1)
set(CONFIG_BOARD "hpm6750")
set(CONFIG_UART_CONSOLE 1)

# Include HPM SDK
find_package(hpm-sdk REQUIRED HINTS $ENV{HPM_SDK_BASE})

# Project definition
project(multi_uart_can_system)

# Source files for Multi-UART+CAN system
set(MULTI_UART_CAN_SOURCES
    src/main_multi_uart_can.c
    src/multi_uart_can_app.c
    src/uart_driver.c
    src/can_driver.c
    src/data_manager.c
    src/multi_uart_can_pinmux.c
)

# Header files
set(MULTI_UART_CAN_HEADERS
    src/multi_uart_can_config.h
    src/multi_uart_can_app.h
    src/uart_driver.h
    src/can_driver.h
    src/data_manager.h
)

# Include directories
include_directories(
    src
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Create executable
sdk_compile_definitions(-DBOARD_SHOW_CLOCK=1)
sdk_compile_definitions(-DBOARD_SHOW_BANNER=1)
sdk_compile_definitions(-DDEBUG_DATA_MANAGER=1)

# Add executable target
sdk_app_src(${MULTI_UART_CAN_SOURCES})

# Link libraries
sdk_app_inc(src)

# Generate targets
generate_ide_projects()

# Custom targets for development
add_custom_target(flash
    COMMAND ${CMAKE_COMMAND} -E echo "Flashing Multi-UART+CAN system..."
    COMMAND openocd -f ${HPM_SDK_BASE}/boards/${CONFIG_BOARD}/openocd.cfg -c "program ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.elf verify reset exit"
    DEPENDS ${PROJECT_NAME}
    COMMENT "Flash the Multi-UART+CAN system to target"
)

add_custom_target(debug
    COMMAND ${CMAKE_COMMAND} -E echo "Starting debug session..."
    COMMAND openocd -f ${HPM_SDK_BASE}/boards/${CONFIG_BOARD}/openocd.cfg &
    COMMAND sleep 2
    COMMAND ${CMAKE_TOOLCHAIN_PREFIX}gdb ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.elf -ex "target remote localhost:3333"
    DEPENDS ${PROJECT_NAME}
    COMMENT "Start debug session for Multi-UART+CAN system"
)

# Print configuration summary
message(STATUS "=== Multi-UART+CAN System Configuration ===")
message(STATUS "Board: ${CONFIG_BOARD}")
message(STATUS "SDK Base: ${HPM_SDK_BASE}")
message(STATUS "Toolchain: ${CMAKE_TOOLCHAIN_PREFIX}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Source Files: ${MULTI_UART_CAN_SOURCES}")
message(STATUS "==========================================")
