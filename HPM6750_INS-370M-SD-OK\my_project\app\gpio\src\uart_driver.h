/*
 * UART驱动管理器头文件
 * 管理5组UART的初始化、DMA接收和中断处理
 */

#ifndef _UART_DRIVER_H_
#define _UART_DRIVER_H_

#include "multi_uart_can_config.h"
#include "hpm_uart_drv.h"
#include "hpm_dma_drv.h"

/* UART状态结构 */
typedef struct {
    uint32_t rx_count;                // 接收计数
    uint8_t data_ready;               // 数据就绪标志
    uint8_t online;                   // 在线状态
    uint32_t error_count;             // 错误计数
} uart_status_t;

/* 函数声明 */

/**
 * @brief 初始化所有UART模块
 * @return 状态码
 */
hpm_stat_t multi_uart_init(void);

/**
 * @brief 初始化单个UART模块
 * @param module_id 模块ID (0-4)
 * @return 状态码
 */
hpm_stat_t uart_module_init(uint8_t module_id);

/**
 * @brief UART数据接收处理
 * @param module_id 模块ID
 */
void uart_rx_data_handler(uint8_t module_id);

/**
 * @brief DMA中断服务程序
 */
void uart_dma_isr(void);

/**
 * @brief 获取UART状态
 * @param module_id 模块ID
 * @param status 状态结构指针
 */
void uart_get_status(uint8_t module_id, uart_status_t *status);

/**
 * @brief 验证模块数据
 * @param data 数据指针
 * @param len 数据长度
 * @return 1: 有效, 0: 无效
 */
uint8_t validate_module_data(const uint8_t *data, uint32_t len);

/**
 * @brief 获取时间戳 (在data_manager.c中实现)
 * @return 时间戳 (毫秒)
 */
extern uint32_t get_timestamp_ms(void);

/* 内部函数声明 */
static hpm_stat_t uart_dma_init(uint8_t module_id);
static hpm_stat_t uart_start_dma_receive(uint8_t module_id);
static hpm_stat_t uart_restart_dma_receive(uint8_t module_id);

#endif /* _UART_DRIVER_H_ */
