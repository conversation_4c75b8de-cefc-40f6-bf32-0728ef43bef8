@echo off
REM Build script for Multi-UART+CAN Communication System
REM Based on HPM6750_INS-370M-SD-OK project

echo ================================================
echo  HPM6750 Multi-UART+CAN Communication System
echo  Build Script
echo ================================================

REM Check if HPM_SDK_BASE is set
if "%HPM_SDK_BASE%"=="" (
    echo ERROR: HPM_SDK_BASE environment variable is not set!
    echo Please set HPM_SDK_BASE to your HPM SDK installation path
    echo Example: set HPM_SDK_BASE=C:\hpm_sdk
    pause
    exit /b 1
)

echo HPM SDK Base: %HPM_SDK_BASE%

REM Create build directory
if not exist "build" (
    mkdir build
    echo Created build directory
)

cd build

echo.
echo Configuring CMake...
cmake .. -G "Ninja" -DBOARD=hpm6750evkmini -DCMAKE_BUILD_TYPE=Release

if %ERRORLEVEL% neq 0 (
    echo ERROR: CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo Building project...
ninja

if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo ================================================
echo  Build completed successfully!
echo ================================================
echo.
echo Output files:
dir /b *.elf *.bin *.hex 2>nul

echo.
echo To flash the firmware:
echo   ninja flash
echo.
echo To debug:
echo   ninja debug
echo.

cd ..
pause
