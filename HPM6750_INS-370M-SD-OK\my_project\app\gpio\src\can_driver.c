/*
 * CAN驱动管理器实现
 * 负责CAN初始化、数据打包和发送功能
 */

#include "can_driver.h"
#include "data_manager.h"
#include "multi_uart_can_config.h"
#include "hpm_can_drv.h"
#include "hpm_clock_drv.h"
#include <string.h>
#include <stdio.h>

/* 全局CAN上下文 */
can_context_t g_can_context;

/* CAN发送队列 */
static can_frame_t can_tx_queue[CAN_TX_QUEUE_SIZE];
static volatile uint8_t tx_queue_head = 0;
static volatile uint8_t tx_queue_tail = 0;
static volatile uint8_t tx_queue_count = 0;

/**
 * @brief 初始化CAN上行链路
 */
hpm_stat_t can_uplink_init(void)
{
    hpm_stat_t status;
    
    // 初始化CAN引脚
    init_can_uplink_pins();
    
    // 初始化CAN时钟
    uint32_t can_freq = init_can_clock(CAN_UPLINK);
    if (can_freq == 0) {
        return status_fail;
    }
    
    // 配置CAN上下文
    g_can_context.can_base = CAN_UPLINK;
    g_can_context.clk_name = CAN_UPLINK_CLK;
    g_can_context.irq_num = CAN_UPLINK_IRQ;
    g_can_context.tx_busy = 0;
    
    // 配置CAN参数
    can_config_t can_config;
    can_get_default_config(&can_config);
    
    can_config.mode = can_mode_normal;
    can_config.baudrate = CAN_BAUDRATE;
    can_config.enable_self_ack = false;
    can_config.disable_stb_retransmission = false;
    can_config.disable_ptb_retransmission = false;
    can_config.enable_tx_buffer_priority_mode = false;
    
    // 配置接收过滤器
    can_filter_config_t filter_config;
    filter_config.index = 0;
    filter_config.enable = true;
    filter_config.can_id_mode = can_id_mode_standard;
    filter_config.filter_mode = can_filter_mode_mask;
    filter_config.code = 0x000;  // 接收所有ID
    filter_config.mask = 0x000;  // 不过滤
    
    can_config.filter_list = &filter_config;
    can_config.filter_list_num = 1;
    
    // 启用中断
    can_config.irq_txrx_enable_mask = CAN_EVENT_TRANSMIT | CAN_EVENT_RECEIVE | CAN_EVENT_ERROR;
    can_config.irq_error_enable_mask = CAN_EVENT_ERROR_WARNING | CAN_EVENT_ERROR_PASSIVE | 
                                      CAN_EVENT_BUS_OFF | CAN_EVENT_ARBITRATION_LOST;
    
    // 初始化CAN控制器
    status = can_init(g_can_context.can_base, &can_config, can_freq);
    if (status != status_success) {
        printf("CAN init failed: %d\n", status);
        return status;
    }
    
    // 启用CAN中断
    intc_m_enable_irq_with_priority(g_can_context.irq_num, 2);
    
    printf("CAN uplink initialized at %d baud\n", CAN_BAUDRATE);
    return status_success;
}

/**
 * @brief 发送数据包通过CAN
 */
hpm_stat_t send_data_via_can(void)
{
    data_packet_t *packet;
    hpm_stat_t status;
    
    // 获取数据包
    status = data_manager_get_packet(&packet);
    if (status != status_success) {
        return status;
    }
    
    // 分割为CAN帧
    can_frame_t frames[CAN_TOTAL_FRAMES];
    uint8_t frame_count;
    
    status = data_manager_split_to_can_frames(packet, frames, &frame_count);
    if (status != status_success) {
        return status;
    }
    
    // 将帧加入发送队列
    for (uint8_t i = 0; i < frame_count; i++) {
        status = can_queue_frame(&frames[i]);
        if (status != status_success) {
            printf("Failed to queue CAN frame %d\n", i);
            return status;
        }
    }
    
    // 启动发送
    can_start_transmission();
    
    return status_success;
}

/**
 * @brief 将CAN帧加入发送队列
 */
static hpm_stat_t can_queue_frame(const can_frame_t *frame)
{
    if (frame == NULL) {
        return status_invalid_argument;
    }
    
    // 检查队列是否已满
    if (tx_queue_count >= CAN_TX_QUEUE_SIZE) {
        return status_fail; // 队列满
    }
    
    // 加入队列
    memcpy(&can_tx_queue[tx_queue_head], frame, sizeof(can_frame_t));
    tx_queue_head = (tx_queue_head + 1) % CAN_TX_QUEUE_SIZE;
    tx_queue_count++;
    
    return status_success;
}

/**
 * @brief 从发送队列获取CAN帧
 */
static hpm_stat_t can_dequeue_frame(can_frame_t *frame)
{
    if (frame == NULL || tx_queue_count == 0) {
        return status_fail;
    }
    
    // 从队列取出
    memcpy(frame, &can_tx_queue[tx_queue_tail], sizeof(can_frame_t));
    tx_queue_tail = (tx_queue_tail + 1) % CAN_TX_QUEUE_SIZE;
    tx_queue_count--;
    
    return status_success;
}

/**
 * @brief 启动CAN发送
 */
static void can_start_transmission(void)
{
    if (g_can_context.tx_busy || tx_queue_count == 0) {
        return;
    }
    
    can_frame_t frame;
    if (can_dequeue_frame(&frame) == status_success) {
        can_transmit_buf_t tx_buf;
        
        // 转换为CAN发送缓冲区格式
        tx_buf.id = frame.id;
        tx_buf.dlc = frame.dlc;
        tx_buf.remote_frame = 0;
        tx_buf.extend_frame = 0;
        memcpy(tx_buf.buffer, frame.data, frame.dlc);
        
        // 发送CAN帧
        hpm_stat_t status = can_send_message_nonblocking(g_can_context.can_base, &tx_buf);
        if (status == status_success) {
            g_can_context.tx_busy = 1;
        } else {
            printf("CAN send failed: %d\n", status);
        }
    }
}

/**
 * @brief CAN发送完成中断处理
 */
void can_tx_complete_handler(void)
{
    g_can_context.tx_busy = 0;
    
    // 继续发送队列中的下一帧
    can_start_transmission();
}

/**
 * @brief CAN接收中断处理
 */
void can_rx_handler(void)
{
    can_receive_buf_t rx_buf;
    hpm_stat_t status;
    
    status = can_read_received_message(g_can_context.can_base, &rx_buf);
    if (status == status_success) {
        // 处理接收到的CAN消息
        printf("CAN RX: ID=0x%03X, DLC=%d\n", rx_buf.id, rx_buf.dlc);
        
        // 这里可以添加对上位机命令的处理
        process_can_command(&rx_buf);
    }
}

/**
 * @brief 处理CAN命令
 */
static void process_can_command(const can_receive_buf_t *rx_buf)
{
    if (rx_buf == NULL || rx_buf->dlc == 0) {
        return;
    }
    
    uint8_t cmd = rx_buf->buffer[0];
    
    switch (cmd) {
        case CAN_CMD_GET_STATUS:
            send_system_status();
            break;
            
        case CAN_CMD_RESET_STATS:
            // 重置统计信息
            memset((void*)data_manager_get_stats(), 0, sizeof(system_stats_t));
            break;
            
        case CAN_CMD_SET_SAMPLE_RATE:
            if (rx_buf->dlc >= 3) {
                uint16_t new_rate = (rx_buf->buffer[1] << 8) | rx_buf->buffer[2];
                // 设置新的采样率（这里需要实现采样率控制）
                printf("Set sample rate to %d Hz\n", new_rate);
            }
            break;
            
        default:
            printf("Unknown CAN command: 0x%02X\n", cmd);
            break;
    }
}

/**
 * @brief 发送系统状态
 */
static void send_system_status(void)
{
    can_frame_t status_frame;
    const system_stats_t *stats = data_manager_get_stats();
    
    status_frame.id = CAN_FRAME_ID_STATUS;
    status_frame.dlc = 8;
    
    // 打包状态信息
    status_frame.data[0] = CAN_PACKET_TYPE_STATUS;
    status_frame.data[1] = (stats->total_packets >> 8) & 0xFF;
    status_frame.data[2] = stats->total_packets & 0xFF;
    status_frame.data[3] = (stats->error_packets >> 8) & 0xFF;
    status_frame.data[4] = stats->error_packets & 0xFF;
    
    // 模块在线状态
    uint8_t online_mask = 0;
    for (int i = 0; i < MODULE_COUNT; i++) {
        if (stats->module_stats[i].online) {
            online_mask |= (1 << i);
        }
    }
    status_frame.data[5] = online_mask;
    status_frame.data[6] = stats->timeout_count & 0xFF;
    status_frame.data[7] = stats->sync_failures & 0xFF;
    
    // 发送状态帧
    can_queue_frame(&status_frame);
    can_start_transmission();
}

/**
 * @brief CAN错误处理
 */
void can_error_handler(uint32_t error_flags)
{
    printf("CAN Error: 0x%08X\n", error_flags);
    
    if (error_flags & CAN_EVENT_BUS_OFF) {
        printf("CAN Bus Off - attempting recovery\n");
        // 尝试恢复总线
        can_reset_controller();
    }
    
    if (error_flags & CAN_EVENT_ERROR_WARNING) {
        printf("CAN Error Warning\n");
    }
    
    if (error_flags & CAN_EVENT_ERROR_PASSIVE) {
        printf("CAN Error Passive\n");
    }
}

/**
 * @brief 重置CAN控制器
 */
static void can_reset_controller(void)
{
    // 重新初始化CAN控制器
    can_deinit(g_can_context.can_base);
    
    // 延时后重新初始化
    for (volatile int i = 0; i < 10000; i++);
    
    can_uplink_init();
}

/**
 * @brief CAN中断服务程序
 */
void can_isr(void)
{
    uint32_t status = can_get_interrupt_flags(g_can_context.can_base);
    
    if (status & CAN_EVENT_TRANSMIT) {
        can_tx_complete_handler();
        can_clear_interrupt_flags(g_can_context.can_base, CAN_EVENT_TRANSMIT);
    }
    
    if (status & CAN_EVENT_RECEIVE) {
        can_rx_handler();
        can_clear_interrupt_flags(g_can_context.can_base, CAN_EVENT_RECEIVE);
    }
    
    if (status & (CAN_EVENT_ERROR_WARNING | CAN_EVENT_ERROR_PASSIVE | 
                  CAN_EVENT_BUS_OFF | CAN_EVENT_ARBITRATION_LOST)) {
        can_error_handler(status);
        can_clear_interrupt_flags(g_can_context.can_base, status);
    }
}

/**
 * @brief 获取CAN状态
 */
void can_get_status(can_status_t *status)
{
    if (status == NULL) {
        return;
    }
    
    status->tx_busy = g_can_context.tx_busy;
    status->queue_count = tx_queue_count;
    status->error_count = 0; // 需要从CAN控制器读取
    
    // 读取CAN控制器状态
    uint32_t can_status = can_get_status_flags(g_can_context.can_base);
    status->bus_off = (can_status & CAN_STATUS_BUS_OFF) ? 1 : 0;
    status->error_warning = (can_status & CAN_STATUS_ERROR_WARNING) ? 1 : 0;
}
