/*
 * 5组UART + 1组CAN通信系统配置头文件
 * 基于HPM6750_INS-370M-SD-OK项目
 */

#ifndef _MULTI_UART_CAN_CONFIG_H_
#define _MULTI_UART_CAN_CONFIG_H_

#include "board.h"
#include "hpm_uart_drv.h"
#include "hpm_can_drv.h"
#include "hpm_dmamux_drv.h"
#include "hpm_dma_drv.h"

/* 系统配置参数 */
#define MODULE_COUNT                5           // 模块数量
#define MODULE_DATA_SIZE           37          // 每个模块数据字节数
#define DATA_SAMPLE_RATE           200         // 数据采样率 200Hz
#define UART_BAUDRATE              115200      // UART波特率
#define CAN_BAUDRATE               500000      // CAN波特率 500Kbps

/* UART配置 */
#define UART_MODULE1               HPM_UART0   // 模块1
#define UART_MODULE1_CLK           clock_uart0
#define UART_MODULE1_IRQ           IRQn_UART0
#define UART_MODULE1_DMA_TX_REQ    HPM_DMA_SRC_UART0_TX
#define UART_MODULE1_DMA_RX_REQ    HPM_DMA_SRC_UART0_RX

#define UART_MODULE2               HPM_UART2   // 模块2
#define UART_MODULE2_CLK           clock_uart2
#define UART_MODULE2_IRQ           IRQn_UART2
#define UART_MODULE2_DMA_TX_REQ    HPM_DMA_SRC_UART2_TX
#define UART_MODULE2_DMA_RX_REQ    HPM_DMA_SRC_UART2_RX

#define UART_MODULE3               HPM_UART3   // 模块3
#define UART_MODULE3_CLK           clock_uart3
#define UART_MODULE3_IRQ           IRQn_UART3
#define UART_MODULE3_DMA_TX_REQ    HPM_DMA_SRC_UART3_TX
#define UART_MODULE3_DMA_RX_REQ    HPM_DMA_SRC_UART3_RX

#define UART_MODULE4               HPM_UART6   // 模块4
#define UART_MODULE4_CLK           clock_uart6
#define UART_MODULE4_IRQ           IRQn_UART6
#define UART_MODULE4_DMA_TX_REQ    HPM_DMA_SRC_UART6_TX
#define UART_MODULE4_DMA_RX_REQ    HPM_DMA_SRC_UART6_RX

#define UART_MODULE5               HPM_UART7   // 模块5
#define UART_MODULE5_CLK           clock_uart7
#define UART_MODULE5_IRQ           IRQn_UART7
#define UART_MODULE5_DMA_TX_REQ    HPM_DMA_SRC_UART7_TX
#define UART_MODULE5_DMA_RX_REQ    HPM_DMA_SRC_UART7_RX

/* CAN配置 */
#define CAN_UPLINK                 HPM_CAN0    // 上位机通信
#define CAN_UPLINK_CLK             clock_can0
#define CAN_UPLINK_IRQ             IRQn_CAN0

/* DMA配置 */
#define DMA_CONTROLLER             HPM_HDMA
#define DMAMUX_CONTROLLER          HPM_DMAMUX
#define DMA_IRQ                    IRQn_HDMA

// DMA通道分配
#define DMA_CH_UART0_RX            0
#define DMA_CH_UART2_RX            1
#define DMA_CH_UART3_RX            2
#define DMA_CH_UART6_RX            3
#define DMA_CH_UART7_RX            4

/* 数据缓冲区配置 */
#define UART_RX_BUFFER_SIZE        (MODULE_DATA_SIZE * 2)  // 双缓冲
#define CAN_TX_BUFFER_SIZE         (MODULE_COUNT * MODULE_DATA_SIZE + 16) // 5组数据+头部
#define DATA_SYNC_TIMEOUT_MS       10         // 数据同步超时时间

/* CAN数据包格式 */
#define CAN_FRAME_ID_BASE          0x100      // CAN帧ID基址
#define CAN_MAX_DATA_LEN           8          // CAN单帧最大数据长度
#define CAN_TOTAL_FRAMES           ((MODULE_COUNT * MODULE_DATA_SIZE + CAN_MAX_DATA_LEN - 1) / CAN_MAX_DATA_LEN)

/* 模块数据结构 */
typedef struct {
    uint8_t module_id;                        // 模块ID (0-4)
    uint8_t data[MODULE_DATA_SIZE];           // 模块数据
    uint32_t timestamp;                       // 时间戳
    uint8_t valid;                            // 数据有效标志
} module_data_t;

/* UART上下文结构 */
typedef struct {
    UART_Type *uart_base;                     // UART基址
    clock_name_t clk_name;                    // 时钟名称
    IRQn_Type irq_num;                        // 中断号
    uint32_t dma_rx_req;                      // DMA RX请求源
    uint8_t dma_rx_ch;                        // DMA RX通道
    uint8_t rx_buffer[UART_RX_BUFFER_SIZE];   // 接收缓冲区
    volatile uint32_t rx_count;               // 接收计数
    volatile uint8_t data_ready;              // 数据就绪标志
} uart_context_t;

/* CAN上下文结构 */
typedef struct {
    CAN_Type *can_base;                       // CAN基址
    clock_name_t clk_name;                    // 时钟名称
    IRQn_Type irq_num;                        // 中断号
    uint8_t tx_buffer[CAN_TX_BUFFER_SIZE];    // 发送缓冲区
    volatile uint8_t tx_busy;                 // 发送忙标志
} can_context_t;

/* 系统数据管理结构 */
typedef struct {
    module_data_t modules[MODULE_COUNT];      // 模块数据数组
    uint32_t sync_counter;                    // 同步计数器
    uint32_t error_counter;                   // 错误计数器
    volatile uint8_t all_data_ready;          // 所有数据就绪标志
} system_data_t;

/* 全局变量声明 */
extern uart_context_t g_uart_contexts[MODULE_COUNT];
extern can_context_t g_can_context;
extern system_data_t g_system_data;

/* 函数声明 */
// 硬件初始化
hpm_stat_t multi_uart_can_init(void);
hpm_stat_t uart_module_init(uint8_t module_id);
hpm_stat_t can_uplink_init(void);

// 引脚配置
void init_multi_uart_pins(void);
void init_can_uplink_pins(void);

// 时钟配置
uint32_t init_uart_clock(UART_Type *uart_base);
uint32_t init_can_clock(CAN_Type *can_base);

// DMA配置
void config_uart_dmamux(uint8_t module_id);

// 配置获取
void get_uart_config(uint8_t module_id, UART_Type **uart_base,
                     clock_name_t *clk_name, IRQn_Type *irq_num,
                     uint32_t *dma_rx_req, uint8_t *dma_rx_ch);

// 数据处理
void process_uart_data(uint8_t module_id);
void sync_and_send_data(void);
hpm_stat_t send_data_via_can(void);

// 中断处理
void uart_dma_isr(uint8_t module_id);
void can_tx_isr(void);

// 工具函数
uint32_t get_system_tick_ms(void);
void reset_module_data(uint8_t module_id);
uint8_t validate_module_data(const uint8_t *data, uint32_t len);

#endif /* _MULTI_UART_CAN_CONFIG_H_ */
