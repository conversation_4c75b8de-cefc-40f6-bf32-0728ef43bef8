/*
 * 5组UART + 1组CAN通信系统主应用
 * 实现数据采集、处理和转发的主要业务逻辑
 */

#include "multi_uart_can_app.h"
#include "uart_driver.h"
#include "can_driver.h"
#include "data_manager.h"
#include "multi_uart_can_config.h"
#include "board.h"
#include "hpm_gptmr_drv.h"
#include "hpm_clock_drv.h"
#include <stdio.h>

/* 应用状态 */
static app_state_t g_app_state = APP_STATE_INIT;
static uint32_t g_sync_timer_count = 0;
static uint32_t g_status_report_count = 0;

/* 定时器配置 */
#define SYNC_TIMER_BASE            HPM_GPTMR0
#define SYNC_TIMER_CH              0
#define SYNC_TIMER_IRQ             IRQn_GPTMR0
#define SYNC_TIMER_CLK             clock_gptmr0

/* 200Hz同步定时器配置 (5ms周期) */
#define SYNC_TIMER_FREQ            200
#define SYNC_TIMER_PERIOD_US       (1000000 / SYNC_TIMER_FREQ)

/**
 * @brief 初始化多路UART+CAN通信系统
 */
hpm_stat_t multi_uart_can_app_init(void)
{
    hpm_stat_t status;
    
    printf("Initializing Multi-UART+CAN Communication System...\n");
    
    // 初始化数据管理器
    status = data_manager_init();
    if (status != status_success) {
        printf("Data manager init failed: %d\n", status);
        return status;
    }
    
    // 初始化UART驱动
    status = multi_uart_init();
    if (status != status_success) {
        printf("UART init failed: %d\n", status);
        return status;
    }
    
    // 初始化CAN驱动
    status = can_uplink_init();
    if (status != status_success) {
        printf("CAN init failed: %d\n", status);
        return status;
    }
    
    // 初始化同步定时器
    status = sync_timer_init();
    if (status != status_success) {
        printf("Sync timer init failed: %d\n", status);
        return status;
    }
    
    g_app_state = APP_STATE_RUNNING;
    printf("System initialized successfully!\n");
    
    return status_success;
}

/**
 * @brief 初始化同步定时器
 */
static hpm_stat_t sync_timer_init(void)
{
    gptmr_channel_config_t config;
    
    // 配置定时器时钟
    clock_add_to_group(SYNC_TIMER_CLK, 0);
    clock_set_source_divider(SYNC_TIMER_CLK, clk_src_osc24m, 1); // 24MHz
    
    // 获取定时器配置
    gptmr_channel_get_default_config(SYNC_TIMER_BASE, &config);
    
    config.mode = gptmr_work_mode_repeat;
    config.synci_edge = gptmr_synci_edge_none;
    config.reload = clock_get_frequency(SYNC_TIMER_CLK) / SYNC_TIMER_FREQ - 1;
    config.cmp_initial_polarity_high = false;
    config.enable_cmp_output = false;
    
    // 初始化定时器通道
    gptmr_channel_config(SYNC_TIMER_BASE, SYNC_TIMER_CH, &config, false);
    gptmr_start_counter(SYNC_TIMER_BASE, SYNC_TIMER_CH);
    
    // 启用定时器中断
    gptmr_enable_irq(SYNC_TIMER_BASE, GPTMR_CH_RLD_IRQ_MASK(SYNC_TIMER_CH));
    intc_m_enable_irq_with_priority(SYNC_TIMER_IRQ, 1);
    
    return status_success;
}

/**
 * @brief 主应用循环
 */
void multi_uart_can_app_run(void)
{
    while (1) {
        switch (g_app_state) {
            case APP_STATE_INIT:
                // 初始化状态，等待初始化完成
                break;
                
            case APP_STATE_RUNNING:
                // 运行状态，处理数据
                app_process_data();
                app_handle_timeout();
                app_status_report();
                break;
                
            case APP_STATE_ERROR:
                // 错误状态，尝试恢复
                app_error_recovery();
                break;
                
            default:
                g_app_state = APP_STATE_ERROR;
                break;
        }
        
        // 短暂延时，避免CPU占用过高
        for (volatile int i = 0; i < 1000; i++);
    }
}

/**
 * @brief 处理数据采集和发送
 */
static void app_process_data(void)
{
    // 检查是否有数据需要同步发送
    if (data_manager_is_all_ready()) {
        hpm_stat_t status = data_manager_sync_and_pack();
        if (status == status_success) {
            status = send_data_via_can();
            if (status != status_success) {
                printf("CAN send failed: %d\n", status);
            }
        } else {
            printf("Data sync failed: %d\n", status);
        }
        
        // 重置数据管理器状态
        data_manager_reset();
    }
}

/**
 * @brief 处理超时情况
 */
static void app_handle_timeout(void)
{
    static uint32_t last_timeout_check = 0;
    uint32_t current_time = get_timestamp_ms();
    
    // 每100ms检查一次超时
    if (current_time - last_timeout_check >= 100) {
        data_manager_handle_timeout();
        last_timeout_check = current_time;
    }
}

/**
 * @brief 状态报告
 */
static void app_status_report(void)
{
    static uint32_t last_report_time = 0;
    uint32_t current_time = get_timestamp_ms();
    
    // 每5秒报告一次状态
    if (current_time - last_report_time >= 5000) {
        const system_stats_t *stats = data_manager_get_stats();
        
        printf("=== System Status Report ===\n");
        printf("Total packets: %lu\n", stats->total_packets);
        printf("Success packets: %lu\n", stats->success_packets);
        printf("Error packets: %lu\n", stats->error_packets);
        printf("Timeout count: %lu\n", stats->timeout_count);
        printf("Sync failures: %lu\n", stats->sync_failures);
        
        printf("Module Status:\n");
        for (int i = 0; i < MODULE_COUNT; i++) {
            printf("  Module %d: %s, RX=%lu, ERR=%lu\n", 
                   i + 1,
                   stats->module_stats[i].online ? "Online" : "Offline",
                   stats->module_stats[i].rx_count,
                   stats->module_stats[i].error_count);
        }
        
        can_status_t can_status;
        can_get_status(&can_status);
        printf("CAN Status: TX_BUSY=%d, QUEUE=%d, ERR=%lu\n",
               can_status.tx_busy, can_status.queue_count, can_status.error_count);
        
        printf("============================\n");
        
        last_report_time = current_time;
        g_status_report_count++;
    }
}

/**
 * @brief 错误恢复处理
 */
static void app_error_recovery(void)
{
    printf("Attempting error recovery...\n");
    
    // 重置数据管理器
    data_manager_reset();
    
    // 检查各模块状态
    for (int i = 0; i < MODULE_COUNT; i++) {
        uart_status_t uart_status;
        uart_get_status(i, &uart_status);
        
        if (!uart_status.online) {
            printf("Module %d offline, attempting restart...\n", i + 1);
            uart_module_init(i);
        }
    }
    
    // 检查CAN状态
    can_status_t can_status;
    can_get_status(&can_status);
    
    if (can_status.bus_off) {
        printf("CAN bus off, attempting restart...\n");
        can_uplink_init();
    }
    
    // 延时后返回运行状态
    for (volatile int i = 0; i < 100000; i++);
    g_app_state = APP_STATE_RUNNING;
}

/**
 * @brief 同步定时器中断处理
 */
void sync_timer_isr(void)
{
    if (gptmr_check_status(SYNC_TIMER_BASE, GPTMR_CH_RLD_STAT_MASK(SYNC_TIMER_CH))) {
        gptmr_clear_status(SYNC_TIMER_BASE, GPTMR_CH_RLD_STAT_MASK(SYNC_TIMER_CH));
        
        g_sync_timer_count++;
        
        // 200Hz同步信号，触发数据采集
        // 这里可以添加精确的时间同步逻辑
    }
}

/**
 * @brief DMA中断处理程序 (从uart_driver.c调用)
 */
SDK_DECLARE_EXT_ISR_M(DMA_IRQ, uart_dma_isr)

/**
 * @brief CAN中断处理程序 (从can_driver.c调用)
 */
SDK_DECLARE_EXT_ISR_M(CAN_UPLINK_IRQ, can_isr)

/**
 * @brief 同步定时器中断处理程序
 */
SDK_DECLARE_EXT_ISR_M(SYNC_TIMER_IRQ, sync_timer_isr)

/**
 * @brief 获取应用状态
 */
app_state_t app_get_state(void)
{
    return g_app_state;
}

/**
 * @brief 设置应用状态
 */
void app_set_state(app_state_t state)
{
    g_app_state = state;
}

/**
 * @brief 获取同步计数器
 */
uint32_t app_get_sync_count(void)
{
    return g_sync_timer_count;
}

/**
 * @brief 应用去初始化
 */
void multi_uart_can_app_deinit(void)
{
    // 停止定时器
    gptmr_stop_counter(SYNC_TIMER_BASE, SYNC_TIMER_CH);
    gptmr_disable_irq(SYNC_TIMER_BASE, GPTMR_CH_RLD_IRQ_MASK(SYNC_TIMER_CH));
    
    // 禁用中断
    intc_m_disable_irq(SYNC_TIMER_IRQ);
    intc_m_disable_irq(DMA_IRQ);
    intc_m_disable_irq(CAN_UPLINK_IRQ);
    
    g_app_state = APP_STATE_INIT;
    
    printf("Application deinitialized\n");
}
