@echo off
REM Clean script for Multi-UART+CAN Communication System

echo ================================================
echo  Cleaning Multi-UART+CAN Communication System
echo ================================================

if exist "build" (
    echo Removing build directory...
    rmdir /s /q "build"
    echo Build directory removed.
) else (
    echo Build directory does not exist.
)

if exist "*.log" (
    echo Removing log files...
    del /q *.log
)

if exist "*.tmp" (
    echo Removing temporary files...
    del /q *.tmp
)

echo.
echo Clean completed!
pause
