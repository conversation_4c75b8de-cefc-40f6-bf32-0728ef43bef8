@echo off
REM Project verification script for Multi-UART+CAN Communication System

echo ================================================
echo  Project Structure Verification
echo ================================================

set ERROR_COUNT=0

echo Checking required files...

REM Check main source files
if not exist "src\main_multi_uart_can.c" (
    echo ERROR: main_multi_uart_can.c not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ main_multi_uart_can.c
)

if not exist "src\multi_uart_can_app.c" (
    echo ERROR: multi_uart_can_app.c not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ multi_uart_can_app.c
)

if not exist "src\multi_uart_can_app.h" (
    echo ERROR: multi_uart_can_app.h not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ multi_uart_can_app.h
)

if not exist "src\multi_uart_can_config.h" (
    echo ERROR: multi_uart_can_config.h not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ multi_uart_can_config.h
)

if not exist "src\multi_uart_can_pinmux.c" (
    echo ERROR: multi_uart_can_pinmux.c not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ multi_uart_can_pinmux.c
)

REM Check driver files
if not exist "src\uart_driver.c" (
    echo ERROR: uart_driver.c not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ uart_driver.c
)

if not exist "src\uart_driver.h" (
    echo ERROR: uart_driver.h not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ uart_driver.h
)

if not exist "src\can_driver.c" (
    echo ERROR: can_driver.c not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ can_driver.c
)

if not exist "src\can_driver.h" (
    echo ERROR: can_driver.h not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ can_driver.h
)

if not exist "src\data_manager.c" (
    echo ERROR: data_manager.c not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ data_manager.c
)

if not exist "src\data_manager.h" (
    echo ERROR: data_manager.h not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ data_manager.h
)

REM Check test files
if not exist "src\system_test.c" (
    echo ERROR: system_test.c not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ system_test.c
)

if not exist "src\system_test.h" (
    echo ERROR: system_test.h not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ system_test.h
)

REM Check configuration files
if not exist "CMakeLists.txt" (
    echo ERROR: CMakeLists.txt not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ CMakeLists.txt
)

if not exist "README_Multi_UART_CAN.md" (
    echo ERROR: README_Multi_UART_CAN.md not found
    set /a ERROR_COUNT+=1
) else (
    echo ✓ README_Multi_UART_CAN.md
)

echo.
echo Checking for old files that should be removed...

REM Check that old files are removed
if exist "src\main.c" (
    echo WARNING: Old main.c still exists
    set /a ERROR_COUNT+=1
)

if exist "src\uart.c" (
    echo WARNING: Old uart.c still exists
    set /a ERROR_COUNT+=1
)

if exist "src\INAV" (
    echo WARNING: Old INAV directory still exists
    set /a ERROR_COUNT+=1
)

if exist "src\Protocol" (
    echo WARNING: Old Protocol directory still exists
    set /a ERROR_COUNT+=1
)

if exist "src\Source" (
    echo WARNING: Old Source directory still exists
    set /a ERROR_COUNT+=1
)

echo.
echo ================================================
if %ERROR_COUNT%==0 (
    echo ✓ Project structure verification PASSED
    echo All required files are present and old files removed.
) else (
    echo ✗ Project structure verification FAILED
    echo Found %ERROR_COUNT% issues that need to be addressed.
)
echo ================================================

echo.
echo File count summary:
echo Source files: 
dir /b src\*.c | find /c /v ""
echo Header files:
dir /b src\*.h | find /c /v ""

echo.
echo Total project size:
for /f "tokens=3" %%a in ('dir /s /-c ^| find "File(s)"') do echo Source code: %%a bytes

pause
