/*
 * 5组UART + 1组CAN通信系统主应用头文件
 * 实现数据采集、处理和转发的主要业务逻辑
 */

#ifndef _MULTI_UART_CAN_APP_H_
#define _MULTI_UART_CAN_APP_H_

#include "multi_uart_can_config.h"
#include "hpm_gptmr_drv.h"

/* 应用状态枚举 */
typedef enum {
    APP_STATE_INIT = 0,               // 初始化状态
    APP_STATE_RUNNING,                // 运行状态
    APP_STATE_ERROR,                  // 错误状态
    APP_STATE_SHUTDOWN                // 关闭状态
} app_state_t;

/* 函数声明 */

/**
 * @brief 初始化多路UART+CAN通信系统
 * @return 状态码
 */
hpm_stat_t multi_uart_can_app_init(void);

/**
 * @brief 主应用循环
 */
void multi_uart_can_app_run(void);

/**
 * @brief 应用去初始化
 */
void multi_uart_can_app_deinit(void);

/**
 * @brief 同步定时器中断处理
 */
void sync_timer_isr(void);

/**
 * @brief 获取应用状态
 * @return 当前应用状态
 */
app_state_t app_get_state(void);

/**
 * @brief 设置应用状态
 * @param state 新的应用状态
 */
void app_set_state(app_state_t state);

/**
 * @brief 获取同步计数器
 * @return 同步计数器值
 */
uint32_t app_get_sync_count(void);

/* 内部函数声明 */
static hpm_stat_t sync_timer_init(void);
static void app_process_data(void);
static void app_handle_timeout(void);
static void app_status_report(void);
static void app_error_recovery(void);

#endif /* _MULTI_UART_CAN_APP_H_ */
