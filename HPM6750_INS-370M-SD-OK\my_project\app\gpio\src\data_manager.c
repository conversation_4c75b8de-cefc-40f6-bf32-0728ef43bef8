/*
 * 数据管理模块实现
 * 负责5组UART数据的缓存、同步和CAN数据包格式化
 */

#include "data_manager.h"
#include "multi_uart_can_config.h"
#include <string.h>
#include <stdio.h>

/* 全局数据管理器实例 */
data_manager_t g_data_manager;

/* 环形缓冲区存储 */
static uint8_t module_ring_buffers[MODULE_COUNT][UART_RX_BUFFER_SIZE * 4];

/**
 * @brief 初始化数据管理器
 */
hpm_stat_t data_manager_init(void)
{
    memset(&g_data_manager, 0, sizeof(data_manager_t));
    
    // 初始化环形缓冲区
    for (int i = 0; i < MODULE_COUNT; i++) {
        g_data_manager.ring_buffer[i] = module_ring_buffers[i];
        g_data_manager.ring_size[i] = sizeof(module_ring_buffers[i]);
        g_data_manager.ring_head[i] = 0;
        g_data_manager.ring_tail[i] = 0;
    }
    
    g_data_manager.sync_state = SYNC_STATE_IDLE;
    g_data_manager.sequence_number = 0;
    
    return status_success;
}

/**
 * @brief 添加模块数据到环形缓冲区
 */
hpm_stat_t data_manager_add_module_data(uint8_t module_id, const uint8_t *data, uint32_t length)
{
    if (module_id >= MODULE_COUNT || data == NULL || length != MODULE_DATA_SIZE) {
        return status_invalid_argument;
    }
    
    uint32_t head = g_data_manager.ring_head[module_id];
    uint32_t tail = g_data_manager.ring_tail[module_id];
    uint32_t size = g_data_manager.ring_size[module_id];
    
    // 检查缓冲区空间
    uint32_t next_head = (head + length) % size;
    if (next_head == tail) {
        // 缓冲区满，丢弃最旧的数据
        g_data_manager.stats.module_stats[module_id].error_count++;
        tail = (tail + MODULE_DATA_SIZE) % size;
        g_data_manager.ring_tail[module_id] = tail;
    }
    
    // 复制数据到环形缓冲区
    for (uint32_t i = 0; i < length; i++) {
        g_data_manager.ring_buffer[module_id][(head + i) % size] = data[i];
    }
    
    g_data_manager.ring_head[module_id] = next_head;
    g_data_manager.stats.module_stats[module_id].rx_count++;
    g_data_manager.stats.module_stats[module_id].last_rx_time = get_timestamp_ms();
    g_data_manager.stats.module_stats[module_id].online = true;
    
    // 标记模块数据就绪
    g_data_manager.ready_modules |= (1 << module_id);
    
    return status_success;
}

/**
 * @brief 从环形缓冲区读取模块数据
 */
static hpm_stat_t read_module_data(uint8_t module_id, uint8_t *data)
{
    if (module_id >= MODULE_COUNT || data == NULL) {
        return status_invalid_argument;
    }
    
    uint32_t head = g_data_manager.ring_head[module_id];
    uint32_t tail = g_data_manager.ring_tail[module_id];
    uint32_t size = g_data_manager.ring_size[module_id];
    
    // 检查是否有足够的数据
    uint32_t available = (head >= tail) ? (head - tail) : (size - tail + head);
    if (available < MODULE_DATA_SIZE) {
        return status_fail;
    }
    
    // 读取数据
    for (uint32_t i = 0; i < MODULE_DATA_SIZE; i++) {
        data[i] = g_data_manager.ring_buffer[module_id][(tail + i) % size];
    }
    
    // 更新尾指针
    g_data_manager.ring_tail[module_id] = (tail + MODULE_DATA_SIZE) % size;
    
    return status_success;
}

/**
 * @brief 检查所有模块数据是否就绪
 */
bool data_manager_is_all_ready(void)
{
    uint8_t expected_mask = (1 << MODULE_COUNT) - 1; // 0x1F for 5 modules
    return (g_data_manager.ready_modules & expected_mask) == expected_mask;
}

/**
 * @brief 同步并打包所有模块数据
 */
hpm_stat_t data_manager_sync_and_pack(void)
{
    if (g_data_manager.sync_state != SYNC_STATE_IDLE) {
        return status_fail;
    }
    
    g_data_manager.sync_state = SYNC_STATE_COLLECTING;
    g_data_manager.sync_start_time = get_timestamp_ms();
    
    // 读取所有模块数据
    for (int i = 0; i < MODULE_COUNT; i++) {
        if (read_module_data(i, &g_data_manager.current_packet.payload[i * MODULE_DATA_SIZE]) != status_success) {
            g_data_manager.sync_state = SYNC_STATE_ERROR;
            g_data_manager.stats.sync_failures++;
            return status_fail;
        }
    }
    
    // 构建数据包头部
    data_packet_header_t *header = &g_data_manager.current_packet.header;
    header->sync_word = DATA_PACKET_SYNC_WORD;
    header->version = DATA_PACKET_VERSION;
    header->packet_type = CAN_PACKET_TYPE_DATA;
    header->sequence = g_data_manager.sequence_number++;
    header->module_mask = g_data_manager.ready_modules;
    header->timestamp = get_timestamp_ms();
    header->data_length = MODULE_COUNT * MODULE_DATA_SIZE;
    
    // 计算校验和
    header->checksum = calculate_checksum(g_data_manager.current_packet.payload, 
                                        header->data_length);
    
    g_data_manager.sync_state = SYNC_STATE_READY;
    g_data_manager.ready_modules = 0; // 清除就绪标志
    g_data_manager.stats.success_packets++;
    
    return status_success;
}

/**
 * @brief 获取打包后的数据包
 */
hpm_stat_t data_manager_get_packet(data_packet_t **packet)
{
    if (packet == NULL) {
        return status_invalid_argument;
    }
    
    if (g_data_manager.sync_state != SYNC_STATE_READY) {
        return status_fail;
    }
    
    *packet = &g_data_manager.current_packet;
    g_data_manager.sync_state = SYNC_STATE_SENDING;
    
    return status_success;
}

/**
 * @brief 将数据包分割为CAN帧
 */
hpm_stat_t data_manager_split_to_can_frames(const data_packet_t *packet, 
                                           can_frame_t *frames, 
                                           uint8_t *frame_count)
{
    if (packet == NULL || frames == NULL || frame_count == NULL) {
        return status_invalid_argument;
    }
    
    uint32_t total_size = sizeof(data_packet_header_t) + packet->header.data_length;
    uint8_t *data_ptr = (uint8_t*)packet;
    uint8_t frame_index = 0;
    uint32_t offset = 0;
    
    while (offset < total_size && frame_index < CAN_TOTAL_FRAMES) {
        frames[frame_index].id = CAN_FRAME_ID_BASE + frame_index;
        frames[frame_index].dlc = (total_size - offset) > CAN_MAX_DATA_LEN ? 
                                  CAN_MAX_DATA_LEN : (total_size - offset);
        
        memcpy(frames[frame_index].data, data_ptr + offset, frames[frame_index].dlc);
        
        offset += frames[frame_index].dlc;
        frame_index++;
    }
    
    *frame_count = frame_index;
    return status_success;
}

/**
 * @brief 重置数据管理器状态
 */
void data_manager_reset(void)
{
    g_data_manager.sync_state = SYNC_STATE_IDLE;
    g_data_manager.ready_modules = 0;
}

/**
 * @brief 处理超时情况
 */
void data_manager_handle_timeout(void)
{
    uint32_t current_time = get_timestamp_ms();
    
    // 检查同步超时
    if (g_data_manager.sync_state == SYNC_STATE_COLLECTING) {
        if (current_time - g_data_manager.sync_start_time > DATA_SYNC_TIMEOUT_MS) {
            g_data_manager.sync_state = SYNC_STATE_ERROR;
            g_data_manager.stats.timeout_count++;
            data_manager_reset();
        }
    }
    
    // 检查模块在线状态
    for (int i = 0; i < MODULE_COUNT; i++) {
        if (current_time - g_data_manager.stats.module_stats[i].last_rx_time > 100) {
            g_data_manager.stats.module_stats[i].online = false;
        }
    }
}

/**
 * @brief 计算校验和
 */
uint16_t calculate_checksum(const uint8_t *data, uint32_t length)
{
    uint16_t checksum = 0;
    for (uint32_t i = 0; i < length; i++) {
        checksum += data[i];
    }
    return checksum;
}

/**
 * @brief 验证数据包完整性
 */
bool validate_data_packet(const data_packet_t *packet)
{
    if (packet == NULL) {
        return false;
    }
    
    if (packet->header.sync_word != DATA_PACKET_SYNC_WORD) {
        return false;
    }
    
    uint16_t calculated_checksum = calculate_checksum(packet->payload, 
                                                    packet->header.data_length);
    return calculated_checksum == packet->header.checksum;
}

/**
 * @brief 获取系统统计信息
 */
const system_stats_t* data_manager_get_stats(void)
{
    return &g_data_manager.stats;
}

/**
 * @brief 更新模块状态
 */
void data_manager_update_module_status(uint8_t module_id, bool online)
{
    if (module_id < MODULE_COUNT) {
        g_data_manager.stats.module_stats[module_id].online = online;
        if (!online) {
            g_data_manager.stats.module_stats[module_id].error_count++;
        }
    }
}

/**
 * @brief 获取当前时间戳
 */
uint32_t get_timestamp_ms(void)
{
    // 使用系统时钟获取毫秒时间戳
    // 这里使用简化实现，实际应使用硬件定时器
    static uint32_t tick_count = 0;
    tick_count++;
    return tick_count; // 每次调用递增，模拟时间流逝
}
