/*
 * UART驱动管理器实现
 * 管理5组UART的初始化、DMA接收和中断处理
 */

#include "uart_driver.h"
#include "data_manager.h"
#include "multi_uart_can_config.h"
#include "hpm_uart_drv.h"
#include "hpm_dma_drv.h"
#include "hpm_dmamux_drv.h"
#include "hpm_clock_drv.h"
#include <string.h>

// 声明外部函数
extern void get_uart_config(uint8_t module_id, UART_Type **uart_base,
                           clock_name_t *clk_name, IRQn_Type *irq_num,
                           uint32_t *dma_rx_req, uint8_t *dma_rx_ch);
extern uint32_t init_uart_clock(UART_Type *uart_base);
extern void config_uart_dmamux(uint8_t module_id);

/* 全局UART上下文数组 */
uart_context_t g_uart_contexts[MODULE_COUNT];

/* DMA描述符 */
static dma_linked_descriptor_t uart_rx_descriptors[MODULE_COUNT];

/**
 * @brief 初始化所有UART模块
 */
hpm_stat_t multi_uart_init(void)
{
    hpm_stat_t status = status_success;
    
    // 初始化引脚配置
    init_multi_uart_pins();
    
    // 初始化每个UART模块
    for (uint8_t i = 0; i < MODULE_COUNT; i++) {
        status = uart_module_init(i);
        if (status != status_success) {
            printf("UART module %d init failed: %d\n", i, status);
            return status;
        }
    }
    
    printf("All UART modules initialized successfully\n");
    return status_success;
}

/**
 * @brief 初始化单个UART模块
 */
hpm_stat_t uart_module_init(uint8_t module_id)
{
    if (module_id >= MODULE_COUNT) {
        return status_invalid_argument;
    }
    
    uart_context_t *ctx = &g_uart_contexts[module_id];
    hpm_stat_t status;
    
    // 获取UART配置参数
    get_uart_config(module_id, &ctx->uart_base, &ctx->clk_name, 
                    &ctx->irq_num, &ctx->dma_rx_req, &ctx->dma_rx_ch);
    
    if (ctx->uart_base == NULL) {
        return status_invalid_argument;
    }
    
    // 初始化时钟
    uint32_t uart_freq = init_uart_clock(ctx->uart_base);
    if (uart_freq == 0) {
        return status_fail;
    }
    
    // 配置UART
    uart_config_t uart_config;
    uart_default_config(ctx->uart_base, &uart_config);
    uart_config.baudrate = UART_BAUDRATE;
    uart_config.src_freq_in_hz = uart_freq;
    uart_config.fifo_enable = true;
    uart_config.dma_enable = true;
    
    status = uart_init(ctx->uart_base, &uart_config);
    if (status != status_success) {
        return status;
    }
    
    // 配置DMA
    status = uart_dma_init(module_id);
    if (status != status_success) {
        return status;
    }
    
    // 启动DMA接收
    status = uart_start_dma_receive(module_id);
    if (status != status_success) {
        return status;
    }
    
    // 初始化上下文
    ctx->rx_count = 0;
    ctx->data_ready = 0;
    
    printf("UART%d (module %d) initialized at %d baud\n", 
           (int)ctx->uart_base, module_id, UART_BAUDRATE);
    
    return status_success;
}

/**
 * @brief 初始化UART DMA配置
 */
static hpm_stat_t uart_dma_init(uint8_t module_id)
{
    uart_context_t *ctx = &g_uart_contexts[module_id];
    
    // 配置DMAMUX
    config_uart_dmamux(module_id);
    
    // 配置DMA描述符
    dma_linked_descriptor_t *desc = &uart_rx_descriptors[module_id];
    
    desc->ctrl = DMA_CHCTRL_SRCREQSEL_SET(ctx->dma_rx_req) |
                 DMA_CHCTRL_DSTREQSEL_SET(DMA_SOC_CHN_TO_DMAMUX_CHN(DMA_CONTROLLER, ctx->dma_rx_ch)) |
                 DMA_CHCTRL_SRCWIDTH_SET(DMA_TRANSFER_WIDTH_BYTE) |
                 DMA_CHCTRL_DSTWIDTH_SET(DMA_TRANSFER_WIDTH_BYTE) |
                 DMA_CHCTRL_SRCMODE_SET(DMA_HANDSHAKE_MODE_HANDSHAKE) |
                 DMA_CHCTRL_DSTMODE_SET(DMA_HANDSHAKE_MODE_NORMAL) |
                 DMA_CHCTRL_SRCADDRCTRL_SET(DMA_ADDRESS_CONTROL_FIXED) |
                 DMA_CHCTRL_DSTADDRCTRL_SET(DMA_ADDRESS_CONTROL_INCREMENT) |
                 DMA_CHCTRL_INTMASK_SET(0);
    
    desc->src_addr = (uint32_t)&ctx->uart_base->RBR;
    desc->dst_addr = (uint32_t)ctx->rx_buffer;
    desc->transize = UART_RX_BUFFER_SIZE;
    desc->llpointer = 0; // 非链式传输
    
    return status_success;
}

/**
 * @brief 启动UART DMA接收
 */
static hpm_stat_t uart_start_dma_receive(uint8_t module_id)
{
    uart_context_t *ctx = &g_uart_contexts[module_id];
    dma_linked_descriptor_t *desc = &uart_rx_descriptors[module_id];
    
    // 配置DMA通道
    dma_setup_channel(DMA_CONTROLLER, ctx->dma_rx_ch, desc, true);
    
    // 启用DMA中断
    dma_enable_channel_interrupt(DMA_CONTROLLER, ctx->dma_rx_ch, 
                                DMA_INTERRUPT_MASK_TC | DMA_INTERRUPT_MASK_ERROR);
    
    // 启用UART DMA接收
    uart_enable_irq(ctx->uart_base, uart_intr_rx_data_avail_or_timeout);
    
    return status_success;
}

/**
 * @brief UART数据接收处理
 */
void uart_rx_data_handler(uint8_t module_id)
{
    if (module_id >= MODULE_COUNT) {
        return;
    }
    
    uart_context_t *ctx = &g_uart_contexts[module_id];
    
    // 检查是否接收到完整的数据包
    uint32_t received_bytes = dma_get_remaining_transfer_size(DMA_CONTROLLER, ctx->dma_rx_ch);
    received_bytes = UART_RX_BUFFER_SIZE - received_bytes;
    
    if (received_bytes >= MODULE_DATA_SIZE) {
        // 验证数据
        if (validate_module_data(ctx->rx_buffer, MODULE_DATA_SIZE)) {
            // 添加到数据管理器
            data_manager_add_module_data(module_id, ctx->rx_buffer, MODULE_DATA_SIZE);
            ctx->rx_count++;
            ctx->data_ready = 1;
            
            // 重启DMA接收
            uart_restart_dma_receive(module_id);
        } else {
            // 数据无效，记录错误
            printf("Invalid data from module %d\n", module_id);
        }
    }
}

/**
 * @brief 重启UART DMA接收
 */
static hpm_stat_t uart_restart_dma_receive(uint8_t module_id)
{
    uart_context_t *ctx = &g_uart_contexts[module_id];
    
    // 停止当前DMA传输
    dma_abort_channel(DMA_CONTROLLER, ctx->dma_rx_ch);
    
    // 清空接收缓冲区
    memset(ctx->rx_buffer, 0, UART_RX_BUFFER_SIZE);
    
    // 重新配置DMA描述符
    uart_rx_descriptors[module_id].dst_addr = (uint32_t)ctx->rx_buffer;
    uart_rx_descriptors[module_id].transize = UART_RX_BUFFER_SIZE;
    
    // 重启DMA
    dma_setup_channel(DMA_CONTROLLER, ctx->dma_rx_ch, 
                     &uart_rx_descriptors[module_id], true);
    
    ctx->data_ready = 0;
    
    return status_success;
}

/**
 * @brief 验证模块数据
 */
uint8_t validate_module_data(const uint8_t *data, uint32_t len)
{
    if (data == NULL || len != MODULE_DATA_SIZE) {
        return 0;
    }
    
    // 简单的数据验证 - 检查是否全为0或全为0xFF
    uint8_t zero_count = 0;
    uint8_t ff_count = 0;
    
    for (uint32_t i = 0; i < len; i++) {
        if (data[i] == 0x00) zero_count++;
        if (data[i] == 0xFF) ff_count++;
    }
    
    // 如果全为0或全为0xFF，认为数据无效
    if (zero_count == len || ff_count == len) {
        return 0;
    }
    
    return 1; // 数据有效
}

/**
 * @brief DMA中断服务程序
 */
void uart_dma_isr(void)
{
    uint32_t status = dma_check_transfer_status(DMA_CONTROLLER, DMA_INTERRUPT_STATUS_TC);
    
    for (uint8_t i = 0; i < MODULE_COUNT; i++) {
        if (status & (1 << g_uart_contexts[i].dma_rx_ch)) {
            // 处理接收完成
            uart_rx_data_handler(i);
            dma_clear_transfer_status(DMA_CONTROLLER, 1 << g_uart_contexts[i].dma_rx_ch);
        }
    }
    
    // 检查错误状态
    status = dma_check_transfer_status(DMA_CONTROLLER, DMA_INTERRUPT_STATUS_ERROR);
    if (status) {
        printf("DMA error: 0x%08x\n", status);
        dma_clear_transfer_status(DMA_CONTROLLER, status);
    }
}

/**
 * @brief 获取UART状态
 */
void uart_get_status(uint8_t module_id, uart_status_t *status)
{
    if (module_id >= MODULE_COUNT || status == NULL) {
        return;
    }
    
    uart_context_t *ctx = &g_uart_contexts[module_id];
    
    status->rx_count = ctx->rx_count;
    status->data_ready = ctx->data_ready;
    status->online = (get_timestamp_ms() - data_manager_get_stats()->module_stats[module_id].last_rx_time) < 100;
}

// get_timestamp_ms函数在data_manager.c中实现
