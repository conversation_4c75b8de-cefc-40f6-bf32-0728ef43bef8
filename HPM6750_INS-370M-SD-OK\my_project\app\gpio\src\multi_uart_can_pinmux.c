/*
 * 5组UART + 1组CAN通信系统引脚配置
 * 基于HPM6750_INS-370M-SD-OK项目
 */

#include "multi_uart_can_config.h"
#include "board.h"

/**
 * @brief 初始化多路UART引脚配置
 */
void init_multi_uart_pins(void)
{
    // UART0 引脚配置 - 模块1
    // PY07(RX), PY06(TX)
    HPM_IOC->PAD[IOC_PAD_PY07].FUNC_CTL = IOC_PY07_FUNC_CTL_UART0_RXD;
    HPM_IOC->PAD[IOC_PAD_PY06].FUNC_CTL = IOC_PY06_FUNC_CTL_UART0_TXD;
    // PY端口需要配置PIOC
    HPM_PIOC->PAD[IOC_PAD_PY07].FUNC_CTL = PIOC_PY07_FUNC_CTL_SOC_PY_07;
    HPM_PIOC->PAD[IOC_PAD_PY06].FUNC_CTL = PIOC_PY06_FUNC_CTL_SOC_PY_06;
    
    // UART2 引脚配置 - 模块2
    // PB21(RX), PB22(TX)
    HPM_IOC->PAD[IOC_PAD_PB21].FUNC_CTL = IOC_PB21_FUNC_CTL_UART2_RXD;
    HPM_IOC->PAD[IOC_PAD_PB22].FUNC_CTL = IOC_PB22_FUNC_CTL_UART2_TXD;
    
    // UART3 引脚配置 - 模块3
    // PE17(RX), PE18(TX)
    HPM_IOC->PAD[IOC_PAD_PE17].FUNC_CTL = IOC_PE17_FUNC_CTL_UART3_RXD;
    HPM_IOC->PAD[IOC_PAD_PE18].FUNC_CTL = IOC_PE18_FUNC_CTL_UART3_TXD;
    
    // UART6 引脚配置 - 模块4
    // PE27(RX), PE28(TX)
    HPM_IOC->PAD[IOC_PAD_PE27].FUNC_CTL = IOC_PE27_FUNC_CTL_UART6_RXD;
    HPM_IOC->PAD[IOC_PAD_PE28].FUNC_CTL = IOC_PE28_FUNC_CTL_UART6_TXD;
    
    // UART7 引脚配置 - 模块5
    // PC02(RX), PC03(TX)
    HPM_IOC->PAD[IOC_PAD_PC02].FUNC_CTL = IOC_PC02_FUNC_CTL_UART7_RXD;
    HPM_IOC->PAD[IOC_PAD_PC03].FUNC_CTL = IOC_PC03_FUNC_CTL_UART7_TXD;
}

/**
 * @brief 初始化CAN上行链路引脚配置
 */
void init_can_uplink_pins(void)
{
    // CAN0 引脚配置 - 上位机通信
    // PB17(RX), PB15(TX)
    HPM_IOC->PAD[IOC_PAD_PB15].FUNC_CTL = IOC_PB15_FUNC_CTL_CAN0_TXD;
    HPM_IOC->PAD[IOC_PAD_PB17].FUNC_CTL = IOC_PB17_FUNC_CTL_CAN0_RXD;
}

/**
 * @brief 初始化UART时钟配置
 * @param uart_base UART基址
 * @return 时钟频率
 */
uint32_t init_uart_clock(UART_Type *uart_base)
{
    uint32_t freq = 0;
    
    if (uart_base == HPM_UART0) {
        clock_set_source_divider(clock_uart0, clk_src_pll1_clk1, 8); // 50MHz
        freq = clock_get_frequency(clock_uart0);
    } else if (uart_base == HPM_UART2) {
        clock_set_source_divider(clock_uart2, clk_src_pll1_clk1, 8); // 50MHz
        freq = clock_get_frequency(clock_uart2);
    } else if (uart_base == HPM_UART3) {
        clock_set_source_divider(clock_uart3, clk_src_pll1_clk1, 8); // 50MHz
        freq = clock_get_frequency(clock_uart3);
    } else if (uart_base == HPM_UART6) {
        clock_set_source_divider(clock_uart6, clk_src_pll1_clk1, 8); // 50MHz
        freq = clock_get_frequency(clock_uart6);
    } else if (uart_base == HPM_UART7) {
        clock_set_source_divider(clock_uart7, clk_src_pll1_clk1, 8); // 50MHz
        freq = clock_get_frequency(clock_uart7);
    }
    
    return freq;
}

/**
 * @brief 初始化CAN时钟配置
 * @param can_base CAN基址
 * @return 时钟频率
 */
uint32_t init_can_clock(CAN_Type *can_base)
{
    uint32_t freq = 0;
    
    if (can_base == HPM_CAN0) {
        // 设置CAN0时钟为80MHz
        clock_set_source_divider(clock_can0, clk_src_pll1_clk1, 5);
        freq = clock_get_frequency(clock_can0);
    }
    
    return freq;
}

/**
 * @brief 配置DMA多路复用器
 * @param module_id 模块ID
 */
void config_uart_dmamux(uint8_t module_id)
{
    switch (module_id) {
        case 0: // UART0
            dmamux_config(DMAMUX_CONTROLLER, 
                         DMA_SOC_CHN_TO_DMAMUX_CHN(DMA_CONTROLLER, DMA_CH_UART0_RX), 
                         UART_MODULE1_DMA_RX_REQ, true);
            break;
            
        case 1: // UART2
            dmamux_config(DMAMUX_CONTROLLER, 
                         DMA_SOC_CHN_TO_DMAMUX_CHN(DMA_CONTROLLER, DMA_CH_UART2_RX), 
                         UART_MODULE2_DMA_RX_REQ, true);
            break;
            
        case 2: // UART3
            dmamux_config(DMAMUX_CONTROLLER, 
                         DMA_SOC_CHN_TO_DMAMUX_CHN(DMA_CONTROLLER, DMA_CH_UART3_RX), 
                         UART_MODULE3_DMA_RX_REQ, true);
            break;
            
        case 3: // UART6
            dmamux_config(DMAMUX_CONTROLLER, 
                         DMA_SOC_CHN_TO_DMAMUX_CHN(DMA_CONTROLLER, DMA_CH_UART6_RX), 
                         UART_MODULE4_DMA_RX_REQ, true);
            break;
            
        case 4: // UART7
            dmamux_config(DMAMUX_CONTROLLER, 
                         DMA_SOC_CHN_TO_DMAMUX_CHN(DMA_CONTROLLER, DMA_CH_UART7_RX), 
                         UART_MODULE5_DMA_RX_REQ, true);
            break;
            
        default:
            break;
    }
}

/**
 * @brief 获取UART配置参数
 * @param module_id 模块ID
 * @param uart_base 返回UART基址
 * @param clk_name 返回时钟名称
 * @param irq_num 返回中断号
 * @param dma_rx_req 返回DMA RX请求源
 * @param dma_rx_ch 返回DMA RX通道
 */
void get_uart_config(uint8_t module_id, UART_Type **uart_base, 
                     clock_name_t *clk_name, IRQn_Type *irq_num,
                     uint32_t *dma_rx_req, uint8_t *dma_rx_ch)
{
    switch (module_id) {
        case 0:
            *uart_base = UART_MODULE1;
            *clk_name = UART_MODULE1_CLK;
            *irq_num = UART_MODULE1_IRQ;
            *dma_rx_req = UART_MODULE1_DMA_RX_REQ;
            *dma_rx_ch = DMA_CH_UART0_RX;
            break;
            
        case 1:
            *uart_base = UART_MODULE2;
            *clk_name = UART_MODULE2_CLK;
            *irq_num = UART_MODULE2_IRQ;
            *dma_rx_req = UART_MODULE2_DMA_RX_REQ;
            *dma_rx_ch = DMA_CH_UART2_RX;
            break;
            
        case 2:
            *uart_base = UART_MODULE3;
            *clk_name = UART_MODULE3_CLK;
            *irq_num = UART_MODULE3_IRQ;
            *dma_rx_req = UART_MODULE3_DMA_RX_REQ;
            *dma_rx_ch = DMA_CH_UART3_RX;
            break;
            
        case 3:
            *uart_base = UART_MODULE4;
            *clk_name = UART_MODULE4_CLK;
            *irq_num = UART_MODULE4_IRQ;
            *dma_rx_req = UART_MODULE4_DMA_RX_REQ;
            *dma_rx_ch = DMA_CH_UART6_RX;
            break;
            
        case 4:
            *uart_base = UART_MODULE5;
            *clk_name = UART_MODULE5_CLK;
            *irq_num = UART_MODULE5_IRQ;
            *dma_rx_req = UART_MODULE5_DMA_RX_REQ;
            *dma_rx_ch = DMA_CH_UART7_RX;
            break;
            
        default:
            *uart_base = NULL;
            break;
    }
}
