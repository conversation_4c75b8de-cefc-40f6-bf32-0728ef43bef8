/*
 * 系统测试程序头文件
 * 用于验证5组UART+1组CAN通信系统的功能
 */

#ifndef _SYSTEM_TEST_H_
#define _SYSTEM_TEST_H_

#include "multi_uart_can_config.h"
#include <stdint.h>

/* 测试结果枚举 */
typedef enum {
    TEST_RESULT_PASS = 0,             // 测试通过
    TEST_RESULT_FAIL,                 // 测试失败
    TEST_RESULT_TIMEOUT,              // 测试超时
    TEST_RESULT_ERROR                 // 测试错误
} test_result_t;

/* 测试统计结构 */
typedef struct {
    uint32_t start_time;              // 测试开始时间
    uint32_t end_time;                // 测试结束时间
    uint32_t total_duration;          // 总测试时间
    
    uint32_t uart_tx_count;           // UART发送计数
    uint32_t uart_rx_count;           // UART接收计数
    uint32_t uart_errors;             // UART错误计数
    
    uint32_t can_tx_count;            // CAN发送计数
    uint32_t can_rx_count;            // CAN接收计数
    uint32_t can_errors;              // CAN错误计数
    
    uint32_t data_packets;            // 数据包计数
    uint32_t data_errors;             // 数据错误计数
    
    uint32_t integration_cycles;      // 集成测试周期数
    uint32_t performance_score;       // 性能评分
} test_stats_t;

/* 函数声明 */

/**
 * @brief 运行系统测试
 * @return 测试结果
 */
test_result_t run_system_test(void);

/**
 * @brief UART环回测试
 * @return 测试结果
 */
static test_result_t test_uart_loopback(void);

/**
 * @brief 数据管理器测试
 * @return 测试结果
 */
static test_result_t test_data_manager(void);

/**
 * @brief CAN通信测试
 * @return 测试结果
 */
static test_result_t test_can_communication(void);

/**
 * @brief 系统集成测试
 * @return 测试结果
 */
static test_result_t test_system_integration(void);

/**
 * @brief 性能测试
 * @return 测试结果
 */
static test_result_t test_system_performance(void);

/**
 * @brief 打印测试结果
 * @param result 测试结果
 */
static void print_test_results(test_result_t result);

#endif /* _SYSTEM_TEST_H_ */
