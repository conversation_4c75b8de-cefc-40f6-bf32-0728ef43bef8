# Copyright (c) 2021-2023 HPMicro
# SPDX-License-Identifier: BSD-3-Clause
# Multi-UART+CAN Communication System
# Based on HPM6750_INS-370M-SD-OK project

cmake_minimum_required(VERSION 3.13)

set(CONFIG_HPM6750 1)

find_package(hpm-sdk REQUIRED HINTS $ENV{HPM_SDK_BASE})

project(multi_uart_can_system)

# Compile definitions
sdk_compile_definitions(-DBOARD_SHOW_CLOCK=1)
sdk_compile_definitions(-DBOARD_SHOW_BANNER=1)
sdk_compile_definitions(-DDEBUG_DATA_MANAGER=1)

# Multi-UART+CAN system source files
sdk_app_src(src/main_multi_uart_can.c)
sdk_app_src(src/multi_uart_can_app.c)
sdk_app_src(src/uart_driver.c)
sdk_app_src(src/can_driver.c)
sdk_app_src(src/data_manager.c)
sdk_app_src(src/multi_uart_can_pinmux.c)
sdk_app_src(src/system_test.c)

# Include directories
sdk_app_inc(src)

generate_ide_projects()
