/*
 * 5组UART + 1组CAN通信系统主函数
 * 基于HPM6750_INS-370M-SD-OK项目
 */

#include "board.h"
#include "multi_uart_can_app.h"
#include "multi_uart_can_config.h"
#include "hpm_clock_drv.h"
#include "hpm_sysctl_drv.h"
#include <stdio.h>

/* 版本信息 */
#define SYSTEM_VERSION_MAJOR       1
#define SYSTEM_VERSION_MINOR       0
#define SYSTEM_VERSION_PATCH       0

/**
 * @brief 系统初始化
 */
static void system_init(void)
{
    // 基础板级初始化
    board_init();
    
    // 打印版本信息
    printf("\n");
    printf("================================================\n");
    printf("  HPM6750 Multi-UART+CAN Communication System\n");
    printf("  Version: %d.%d.%d\n", 
           SYSTEM_VERSION_MAJOR, 
           SYSTEM_VERSION_MINOR, 
           SYSTEM_VERSION_PATCH);
    printf("  Build Date: %s %s\n", __DATE__, __TIME__);
    printf("================================================\n");
    printf("System Configuration:\n");
    printf("  - UART Modules: %d\n", MODULE_COUNT);
    printf("  - Data Size per Module: %d bytes\n", MODULE_DATA_SIZE);
    printf("  - Sample Rate: %d Hz\n", DATA_SAMPLE_RATE);
    printf("  - UART Baudrate: %d bps\n", UART_BAUDRATE);
    printf("  - CAN Baudrate: %d bps\n", CAN_BAUDRATE);
    printf("================================================\n");
}

/**
 * @brief 打印系统资源使用情况
 */
static void print_system_resources(void)
{
    printf("\nSystem Resources:\n");
    printf("  UART Modules Used:\n");
    printf("    - UART0 (Module 1): PY07/PY06\n");
    printf("    - UART2 (Module 2): PB21/PB22\n");
    printf("    - UART3 (Module 3): PE17/PE18\n");
    printf("    - UART6 (Module 4): PE27/PE28\n");
    printf("    - UART7 (Module 5): PC02/PC03\n");
    printf("  CAN Module Used:\n");
    printf("    - CAN0 (Uplink): PB17/PB15\n");
    printf("  DMA Channels Used: 0-4 (UART RX)\n");
    printf("  Timer Used: GPTMR0 (200Hz Sync)\n");
    printf("  Memory Usage:\n");
    printf("    - UART RX Buffers: %d bytes\n", MODULE_COUNT * UART_RX_BUFFER_SIZE);
    printf("    - CAN TX Buffer: %d bytes\n", CAN_TX_BUFFER_SIZE);
    printf("    - Data Packet Size: %d bytes\n", (int)sizeof(data_packet_t));
    printf("\n");
}

/**
 * @brief 系统自检
 */
static hpm_stat_t system_self_test(void)
{
    printf("Performing system self-test...\n");
    
    // 检查时钟配置
    uint32_t cpu_freq = clock_get_frequency(clock_cpu0);
    uint32_t ahb_freq = clock_get_frequency(clock_ahb);
    
    printf("  CPU Frequency: %lu Hz\n", cpu_freq);
    printf("  AHB Frequency: %lu Hz\n", ahb_freq);
    
    if (cpu_freq < 100000000) { // 至少100MHz
        printf("  ERROR: CPU frequency too low!\n");
        return status_fail;
    }
    
    // 检查UART时钟
    for (int i = 0; i < MODULE_COUNT; i++) {
        UART_Type *uart_base;
        clock_name_t clk_name;
        IRQn_Type irq_num;
        uint32_t dma_rx_req;
        uint8_t dma_rx_ch;
        
        get_uart_config(i, &uart_base, &clk_name, &irq_num, &dma_rx_req, &dma_rx_ch);
        
        if (uart_base == NULL) {
            printf("  ERROR: UART%d configuration invalid!\n", i);
            return status_fail;
        }
        
        uint32_t uart_freq = clock_get_frequency(clk_name);
        printf("  UART%d Frequency: %lu Hz\n", i, uart_freq);
        
        if (uart_freq == 0) {
            printf("  ERROR: UART%d clock not configured!\n", i);
            return status_fail;
        }
    }
    
    // 检查CAN时钟
    uint32_t can_freq = clock_get_frequency(CAN_UPLINK_CLK);
    printf("  CAN0 Frequency: %lu Hz\n", can_freq);
    
    if (can_freq == 0) {
        printf("  ERROR: CAN clock not configured!\n");
        return status_fail;
    }
    
    printf("System self-test completed successfully!\n\n");
    return status_success;
}

/**
 * @brief 主函数
 */
int main(void)
{
    hpm_stat_t status;
    
    // 系统初始化
    system_init();
    
    // 打印资源使用情况
    print_system_resources();
    
    // 系统自检
    status = system_self_test();
    if (status != status_success) {
        printf("System self-test failed! Halting...\n");
        while (1) {
            // 系统自检失败，停止运行
            for (volatile int i = 0; i < 1000000; i++);
        }
    }
    
    // 初始化多路UART+CAN通信系统
    status = multi_uart_can_app_init();
    if (status != status_success) {
        printf("Application initialization failed: %d\n", status);
        printf("System halted!\n");
        while (1) {
            // 应用初始化失败，停止运行
            for (volatile int i = 0; i < 1000000; i++);
        }
    }
    
    printf("System startup completed successfully!\n");
    printf("Starting main application loop...\n\n");
    
    // 运行主应用循环
    multi_uart_can_app_run();
    
    // 正常情况下不会到达这里
    printf("Application terminated unexpectedly!\n");
    multi_uart_can_app_deinit();
    
    return 0;
}

/**
 * @brief 系统错误处理
 */
void system_error_handler(const char *error_msg)
{
    printf("SYSTEM ERROR: %s\n", error_msg);
    
    // 设置应用状态为错误
    app_set_state(APP_STATE_ERROR);
    
    // 可以在这里添加错误日志记录、LED指示等
}

/**
 * @brief 断言失败处理
 */
void assert_failed(uint8_t *file, uint32_t line)
{
    printf("Assert failed: %s:%lu\n", file, line);
    system_error_handler("Assert failed");
    
    while (1) {
        // 断言失败，停止运行
        for (volatile int i = 0; i < 1000000; i++);
    }
}

/**
 * @brief 硬件错误处理
 */
void HardFault_Handler(void)
{
    printf("Hard Fault occurred!\n");
    system_error_handler("Hard Fault");
    
    while (1) {
        // 硬件错误，停止运行
        for (volatile int i = 0; i < 1000000; i++);
    }
}
