/*
 * CAN驱动管理器头文件
 * 负责CAN初始化、数据打包和发送功能
 */

#ifndef _CAN_DRIVER_H_
#define _CAN_DRIVER_H_

#include "multi_uart_can_config.h"
#include "hpm_can_drv.h"

/* CAN发送队列大小 */
#define CAN_TX_QUEUE_SIZE          32

/* CAN帧ID定义 */
#define CAN_FRAME_ID_STATUS        0x200
#define CAN_FRAME_ID_ERROR         0x300

/* CAN命令定义 */
#define CAN_CMD_GET_STATUS         0x01
#define CAN_CMD_RESET_STATS        0x02
#define CAN_CMD_SET_SAMPLE_RATE    0x03

/* CAN状态结构 */
typedef struct {
    uint8_t tx_busy;                  // 发送忙标志
    uint8_t queue_count;              // 队列中帧数量
    uint32_t error_count;             // 错误计数
    uint8_t bus_off;                  // 总线关闭标志
    uint8_t error_warning;            // 错误警告标志
} can_status_t;

/* 函数声明 */

/**
 * @brief 初始化CAN上行链路
 * @return 状态码
 */
hpm_stat_t can_uplink_init(void);

/**
 * @brief 发送数据包通过CAN
 * @return 状态码
 */
hpm_stat_t send_data_via_can(void);

/**
 * @brief CAN发送完成中断处理
 */
void can_tx_complete_handler(void);

/**
 * @brief CAN接收中断处理
 */
void can_rx_handler(void);

/**
 * @brief CAN错误处理
 * @param error_flags 错误标志
 */
void can_error_handler(uint32_t error_flags);

/**
 * @brief CAN中断服务程序
 */
void can_isr(void);

/**
 * @brief 获取CAN状态
 * @param status 状态结构指针
 */
void can_get_status(can_status_t *status);

/* 内部函数声明 */
static hpm_stat_t can_queue_frame(const can_frame_t *frame);
static hpm_stat_t can_dequeue_frame(can_frame_t *frame);
static void can_start_transmission(void);
static void process_can_command(const can_receive_buf_t *rx_buf);
static void send_system_status(void);
static void can_reset_controller(void);

#endif /* _CAN_DRIVER_H_ */
