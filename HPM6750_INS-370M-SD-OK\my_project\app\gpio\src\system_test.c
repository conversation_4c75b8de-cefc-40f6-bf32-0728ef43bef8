/*
 * 系统测试程序
 * 用于验证5组UART+1组CAN通信系统的功能
 */

#include "system_test.h"
#include "multi_uart_can_app.h"
#include "uart_driver.h"
#include "can_driver.h"
#include "data_manager.h"
#include <stdio.h>
#include <string.h>

/* 测试配置 */
#define TEST_DURATION_MS           10000    // 测试持续时间10秒
#define TEST_DATA_PATTERN_SIZE     37       // 测试数据模式大小

/* 测试数据模式 */
static const uint8_t test_data_patterns[MODULE_COUNT][TEST_DATA_PATTERN_SIZE] = {
    // 模块1测试数据
    {0xAA, 0x55, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
     0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10, 0x11, 0x12,
     0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x1B, 0x1C,
     0x1D, 0x1E, 0x1F, 0x20, 0x21, 0x22, 0x23},
    
    // 模块2测试数据
    {0x55, 0xAA, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2A, 0x2B,
     0x2C, 0x2D, 0x2E, 0x2F, 0x30, 0x31, 0x32, 0x33, 0x34, 0x35,
     0x36, 0x37, 0x38, 0x39, 0x3A, 0x3B, 0x3C, 0x3D, 0x3E, 0x3F,
     0x40, 0x41, 0x42, 0x43, 0x44, 0x45, 0x46},
    
    // 模块3测试数据
    {0xFF, 0x00, 0x47, 0x48, 0x49, 0x4A, 0x4B, 0x4C, 0x4D, 0x4E,
     0x4F, 0x50, 0x51, 0x52, 0x53, 0x54, 0x55, 0x56, 0x57, 0x58,
     0x59, 0x5A, 0x5B, 0x5C, 0x5D, 0x5E, 0x5F, 0x60, 0x61, 0x62,
     0x63, 0x64, 0x65, 0x66, 0x67, 0x68, 0x69},
    
    // 模块4测试数据
    {0x00, 0xFF, 0x6A, 0x6B, 0x6C, 0x6D, 0x6E, 0x6F, 0x70, 0x71,
     0x72, 0x73, 0x74, 0x75, 0x76, 0x77, 0x78, 0x79, 0x7A, 0x7B,
     0x7C, 0x7D, 0x7E, 0x7F, 0x80, 0x81, 0x82, 0x83, 0x84, 0x85,
     0x86, 0x87, 0x88, 0x89, 0x8A, 0x8B, 0x8C},
    
    // 模块5测试数据
    {0xA5, 0x5A, 0x8D, 0x8E, 0x8F, 0x90, 0x91, 0x92, 0x93, 0x94,
     0x95, 0x96, 0x97, 0x98, 0x99, 0x9A, 0x9B, 0x9C, 0x9D, 0x9E,
     0x9F, 0xA0, 0xA1, 0xA2, 0xA3, 0xA4, 0xA5, 0xA6, 0xA7, 0xA8,
     0xA9, 0xAA, 0xAB, 0xAC, 0xAD, 0xAE, 0xAF}
};

/* 测试统计 */
static test_stats_t g_test_stats;

/**
 * @brief 运行系统测试
 */
test_result_t run_system_test(void)
{
    printf("\n=== Starting System Test ===\n");
    
    // 初始化测试统计
    memset(&g_test_stats, 0, sizeof(test_stats_t));
    g_test_stats.start_time = get_timestamp_ms();
    
    // 运行各项测试
    test_result_t result = TEST_RESULT_PASS;
    
    // 1. UART环回测试
    printf("1. Running UART loopback test...\n");
    if (test_uart_loopback() != TEST_RESULT_PASS) {
        printf("   UART loopback test FAILED!\n");
        result = TEST_RESULT_FAIL;
    } else {
        printf("   UART loopback test PASSED\n");
    }
    
    // 2. 数据管理器测试
    printf("2. Running data manager test...\n");
    if (test_data_manager() != TEST_RESULT_PASS) {
        printf("   Data manager test FAILED!\n");
        result = TEST_RESULT_FAIL;
    } else {
        printf("   Data manager test PASSED\n");
    }
    
    // 3. CAN通信测试
    printf("3. Running CAN communication test...\n");
    if (test_can_communication() != TEST_RESULT_PASS) {
        printf("   CAN communication test FAILED!\n");
        result = TEST_RESULT_FAIL;
    } else {
        printf("   CAN communication test PASSED\n");
    }
    
    // 4. 系统集成测试
    printf("4. Running system integration test...\n");
    if (test_system_integration() != TEST_RESULT_PASS) {
        printf("   System integration test FAILED!\n");
        result = TEST_RESULT_FAIL;
    } else {
        printf("   System integration test PASSED\n");
    }
    
    // 5. 性能测试
    printf("5. Running performance test...\n");
    if (test_system_performance() != TEST_RESULT_PASS) {
        printf("   Performance test FAILED!\n");
        result = TEST_RESULT_FAIL;
    } else {
        printf("   Performance test PASSED\n");
    }
    
    g_test_stats.end_time = get_timestamp_ms();
    g_test_stats.total_duration = g_test_stats.end_time - g_test_stats.start_time;
    
    // 打印测试结果
    print_test_results(result);
    
    return result;
}

/**
 * @brief UART环回测试
 */
static test_result_t test_uart_loopback(void)
{
    // 模拟UART数据接收测试
    for (uint8_t module_id = 0; module_id < MODULE_COUNT; module_id++) {
        // 模拟接收测试数据
        hpm_stat_t status = data_manager_add_module_data(module_id, 
                                                        test_data_patterns[module_id], 
                                                        TEST_DATA_PATTERN_SIZE);
        if (status != status_success) {
            printf("   Module %d data add failed: %d\n", module_id + 1, status);
            g_test_stats.uart_errors++;
            return TEST_RESULT_FAIL;
        }
        
        g_test_stats.uart_tx_count++;
        g_test_stats.uart_rx_count++;
    }
    
    return TEST_RESULT_PASS;
}

/**
 * @brief 数据管理器测试
 */
static test_result_t test_data_manager(void)
{
    // 测试数据同步
    if (!data_manager_is_all_ready()) {
        printf("   Data not ready after adding all modules\n");
        return TEST_RESULT_FAIL;
    }
    
    // 测试数据打包
    hpm_stat_t status = data_manager_sync_and_pack();
    if (status != status_success) {
        printf("   Data sync and pack failed: %d\n", status);
        g_test_stats.data_errors++;
        return TEST_RESULT_FAIL;
    }
    
    // 测试数据包获取
    data_packet_t *packet;
    status = data_manager_get_packet(&packet);
    if (status != status_success) {
        printf("   Get packet failed: %d\n", status);
        g_test_stats.data_errors++;
        return TEST_RESULT_FAIL;
    }
    
    // 验证数据包内容
    if (!validate_data_packet(packet)) {
        printf("   Data packet validation failed\n");
        g_test_stats.data_errors++;
        return TEST_RESULT_FAIL;
    }
    
    g_test_stats.data_packets++;
    return TEST_RESULT_PASS;
}

/**
 * @brief CAN通信测试
 */
static test_result_t test_can_communication(void)
{
    // 获取数据包
    data_packet_t *packet;
    hmp_stat_t status = data_manager_get_packet(&packet);
    if (status != status_success) {
        printf("   No packet available for CAN test\n");
        return TEST_RESULT_FAIL;
    }
    
    // 分割为CAN帧
    can_frame_t frames[CAN_TOTAL_FRAMES];
    uint8_t frame_count;
    
    status = data_manager_split_to_can_frames(packet, frames, &frame_count);
    if (status != status_success) {
        printf("   CAN frame split failed: %d\n", status);
        g_test_stats.can_errors++;
        return TEST_RESULT_FAIL;
    }
    
    // 验证CAN帧
    if (frame_count == 0 || frame_count > CAN_TOTAL_FRAMES) {
        printf("   Invalid CAN frame count: %d\n", frame_count);
        g_test_stats.can_errors++;
        return TEST_RESULT_FAIL;
    }
    
    // 模拟CAN发送
    for (uint8_t i = 0; i < frame_count; i++) {
        if (frames[i].dlc > 8) {
            printf("   Invalid CAN frame DLC: %d\n", frames[i].dlc);
            g_test_stats.can_errors++;
            return TEST_RESULT_FAIL;
        }
        g_test_stats.can_tx_count++;
    }
    
    return TEST_RESULT_PASS;
}

/**
 * @brief 系统集成测试
 */
static test_result_t test_system_integration(void)
{
    uint32_t test_cycles = 10;
    
    for (uint32_t cycle = 0; cycle < test_cycles; cycle++) {
        // 重置数据管理器
        data_manager_reset();
        
        // 模拟完整的数据流
        for (uint8_t module_id = 0; module_id < MODULE_COUNT; module_id++) {
            // 修改测试数据以模拟实时数据
            uint8_t test_data[TEST_DATA_PATTERN_SIZE];
            memcpy(test_data, test_data_patterns[module_id], TEST_DATA_PATTERN_SIZE);
            test_data[0] = cycle; // 添加周期标识
            
            hpm_stat_t status = data_manager_add_module_data(module_id, test_data, TEST_DATA_PATTERN_SIZE);
            if (status != status_success) {
                printf("   Integration test cycle %lu, module %d failed\n", cycle, module_id + 1);
                return TEST_RESULT_FAIL;
            }
        }
        
        // 同步和发送
        if (data_manager_is_all_ready()) {
            hpm_stat_t status = data_manager_sync_and_pack();
            if (status != status_success) {
                printf("   Integration test cycle %lu sync failed\n", cycle);
                return TEST_RESULT_FAIL;
            }
            
            // 模拟CAN发送
            status = send_data_via_can();
            if (status != status_success) {
                printf("   Integration test cycle %lu CAN send failed\n", cycle);
                return TEST_RESULT_FAIL;
            }
        }
        
        g_test_stats.integration_cycles++;
    }
    
    return TEST_RESULT_PASS;
}

/**
 * @brief 性能测试
 */
static test_result_t test_system_performance(void)
{
    uint32_t start_time = get_timestamp_ms();
    uint32_t test_packets = 100;
    
    for (uint32_t i = 0; i < test_packets; i++) {
        // 重置状态
        data_manager_reset();
        
        // 添加数据
        for (uint8_t module_id = 0; module_id < MODULE_COUNT; module_id++) {
            data_manager_add_module_data(module_id, 
                                       test_data_patterns[module_id], 
                                       TEST_DATA_PATTERN_SIZE);
        }
        
        // 处理数据
        if (data_manager_is_all_ready()) {
            data_manager_sync_and_pack();
            send_data_via_can();
        }
    }
    
    uint32_t end_time = get_timestamp_ms();
    uint32_t duration = end_time - start_time;
    
    // 计算性能指标
    float packets_per_second = (float)test_packets * 1000.0f / (float)duration;
    float data_rate_kbps = packets_per_second * (MODULE_COUNT * MODULE_DATA_SIZE) * 8.0f / 1000.0f;
    
    printf("   Performance Results:\n");
    printf("     Test Duration: %lu ms\n", duration);
    printf("     Packets/Second: %.2f\n", packets_per_second);
    printf("     Data Rate: %.2f kbps\n", data_rate_kbps);
    
    // 检查性能要求 (200Hz = 200 packets/second)
    if (packets_per_second < 200.0f) {
        printf("   Performance test FAILED: %.2f < 200 packets/second\n", packets_per_second);
        return TEST_RESULT_FAIL;
    }
    
    g_test_stats.performance_score = (uint32_t)packets_per_second;
    return TEST_RESULT_PASS;
}

/**
 * @brief 打印测试结果
 */
static void print_test_results(test_result_t result)
{
    printf("\n=== Test Results Summary ===\n");
    printf("Overall Result: %s\n", (result == TEST_RESULT_PASS) ? "PASS" : "FAIL");
    printf("Test Duration: %lu ms\n", g_test_stats.total_duration);
    printf("\nDetailed Statistics:\n");
    printf("  UART TX Count: %lu\n", g_test_stats.uart_tx_count);
    printf("  UART RX Count: %lu\n", g_test_stats.uart_rx_count);
    printf("  UART Errors: %lu\n", g_test_stats.uart_errors);
    printf("  CAN TX Count: %lu\n", g_test_stats.can_tx_count);
    printf("  CAN Errors: %lu\n", g_test_stats.can_errors);
    printf("  Data Packets: %lu\n", g_test_stats.data_packets);
    printf("  Data Errors: %lu\n", g_test_stats.data_errors);
    printf("  Integration Cycles: %lu\n", g_test_stats.integration_cycles);
    printf("  Performance Score: %lu packets/sec\n", g_test_stats.performance_score);
    printf("============================\n");
}
